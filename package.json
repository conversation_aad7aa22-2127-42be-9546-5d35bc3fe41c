{"name": "express-vercel-mock", "version": "1.0.0", "description": "A simple Node.js Express API with mock response data deployed on Vercel.", "main": "server.js", "type": "module", "bin": {"mockapi": "./cli/cli.js"}, "scripts": {"start": "node server.js", "dev": "nodemon server.js", "format": "prettier --write ."}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "inquirer": "^12.5.0"}, "devDependencies": {"nodemon": "^3.0.2", "prettier": "^3.5.3"}, "engines": {"node": ">=18"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON>"}