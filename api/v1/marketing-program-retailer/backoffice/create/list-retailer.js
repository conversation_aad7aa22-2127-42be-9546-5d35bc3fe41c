import express from "express";
const router = express.Router();

// Mock data
const mockData = {
    success: true,
    message: "Mock data retrieved successfully!",
    data: [
        {
            id: "36ff815d-1fa3-4f1c-a4ef-9b2912d9d892",
            name: "rrrr",
            sub_area: "Coba",
        },
    ],
};

// Define API route
router.get("/", (req, res) => {
    res.json(mockData);
});

export default router;
