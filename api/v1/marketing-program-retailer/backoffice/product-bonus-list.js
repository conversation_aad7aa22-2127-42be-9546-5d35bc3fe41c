import express from "express";
const router = express.Router();

// Mock data
const mockData = {
    success: true,
    message: "Mock data retrieved successfully!",
    data: [
        {
            variant_id: "a2c712db-53bc-4f2c-a4e5-b3ed44bfafa9",
            variant_name: "ABAPRO 36 EC 500 ML - 40BTL",
            sale_unit: "BOX",
            price_unit: "1000000",
        },
        {
            variant_id: "2b55f771-9236-4e57-aad8-2452344f465d",
            variant_name: "AGCEL 500 SL 1,000 ML",
            sale_unit: "BOX",
            price_unit: "1000000",
        },
        {
            variant_id: "8588108b-d78d-4e6e-b8bc-34b997ea19cd",
            variant_name: "AGCEL 500 SL 250 ML",
            sale_unit: "BOX",
            price_unit: "1000000",
        },
        {
            variant_id: "97b7c1a1-b325-4b24-90c6-db64c4c2620e",
            variant_name: "AGCEL 500 SL 500 ML - 40BTL",
            sale_unit: "BOX",
            price_unit: "1000000",
        },
        {
            variant_id: "38d81017-7200-11ee-a1d4-0a011f33cfb2",
            variant_name: "AKTIV 100 ML",
            sale_unit: "BOX",
            price_unit: "1000000",
        },
        {
            variant_id: "38d819e4-7200-11ee-a1d4-0a011f33cfb2",
            variant_name: "AKTIV 100 ML - 100 BTL",
            sale_unit: "BOX",
            price_unit: "1000000",
        },
    ],
    current_page: 0,
    has_next: true,
    is_first: true,
    is_last: false,
    total_data: 20,
};

// Define API route
router.get("/", (req, res) => {
    // Extract query parameters
    const page = parseInt(req.query.page) || 0; // Default to page 0
    const size = parseInt(req.query.size) || 10; // Default page size 10
    const { string_filter: stringFilter = "" } = req.query;
    const normalizedStringFilter = String(stringFilter).toLowerCase();

    let filteredData = mockData.data;
    if (normalizedStringFilter)
        filteredData = mockData.data.filter((item) =>
            item.variant_name.toLowerCase().includes(normalizedStringFilter)
        );

    // Calculate pagination
    const startIndex = page * size;
    const endIndex = startIndex + size;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    res.json({
        success: true,
        message: "Product bonus list retrieved successfully!",
        page,
        size,
        totalItems: mockData.length,
        totalPages: Math.ceil(mockData.length / size),
        data: paginatedData,
    });
});

export default router;
