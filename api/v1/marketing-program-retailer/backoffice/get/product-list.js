import express from "express";
const router = express.Router();

// Mock data
const mockData = {
    success: true,
    message: "Mock data retrieved successfully!",
    data: [
        {
            id: "5454ce86-486a-4b69-af23-27f3fa350024",
            name: "AGCEL 500 SL (BRAND)",
            delivery_unit: "LT",
            is_variant: false,
        },
        {
            id: "8588108b-d78d-4e6e-b8bc-34b997ea19cd",
            name: "AGCEL 500 SL 250 ML",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "154211e6-5833-46c5-a9b9-4e943726d5da",
            name: "AKTIV (BRAND)",
            delivery_unit: "LT",
            is_variant: false,
        },
        {
            id: "38d819e4-7200-11ee-a1d4-0a011f33cfb2",
            name: "AKTIV 500 ML",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "5e75517c-476f-44e6-bbed-1ac06276350f",
            name: "AMUNISI 100 EC (BRAND)",
            delivery_unit: "LT",
            is_variant: false,
        },
        {
            id: "a6d9a35c-9be9-4d31-ac7f-c4130f9c1624",
            name: "AMUNISI 100 EC 100 ML - 100 BTL",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "dee2114a-f024-42ac-bbf4-e41ad219d9f1",
            name: "AMUNISI 100 EC 500 ML - 40BTL",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "4a95d8b0-395b-4cf5-aa30-e018b9b6cfe8",
            name: "DINAMEC 10 WG (BRAND)",
            delivery_unit: "KG",
            is_variant: false,
        },
        {
            id: "38d802cd-7200-11ee-a1d4-0a011f33cfb2",
            name: "DINAMEC 10WG 50 GR",
            delivery_unit: "KG",
            is_variant: true,
        },
        {
            id: "11b98d52-1a90-46e3-9399-fab95e71ea5c",
            name: "HUNTER 200 EC (BRAND)",
            delivery_unit: "LT",
            is_variant: false,
        },
        {
            id: "28f91986-b6c2-42f8-b846-d0fc996b6c51",
            name: "PAKET JAGUNGKLIN SEDANG",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "6a383156-c41b-4128-89f2-66da95fb7e91",
            name: "PAKET JAGUNGKLIN KECIL",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "f5c89fa1-a843-4dd7-b661-8cf5d1b16e7b",
            name: "PAKET JAGUNGKLIN BESAR",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "203e5759-8e97-4616-939f-25fdd8aaeb13",
            name: "PAKET JAGUNGKLIN (BRAND)",
            delivery_unit: "LT",
            is_variant: false,
        },
        {
            id: "2729b0b0-71e2-4f3d-8771-a265d1baf3cf",
            name: "BUFOS 150 SL (BRAND)",
            delivery_unit: "LT",
            is_variant: false,
        },
        {
            id: "38d80657-7200-11ee-a1d4-0a011f33cfb2",
            name: "BUFOS 150SL 20,000 ML",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "5f389d84-11c4-4cec-90c3-c9e0a928d9cf",
            name: "BUFOS 100SL 5,000 ML",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "fe226cd2-0340-43d4-b1ef-8be764eeba53",
            name: "SUPERFOS 550 EC 100 ML - 100BTL",
            delivery_unit: "LT",
            is_variant: true,
        },
        {
            id: "99ec3cea-0131-4308-8ae7-b1d918bff692",
            name: "MAXXFOSATE 490 SL (BRAND)",
            delivery_unit: "LT",
            is_variant: false,
        },
    ],
};

// Define API route
router.get("/", (req, res) => {
    res.json(mockData);
});

export default router;
