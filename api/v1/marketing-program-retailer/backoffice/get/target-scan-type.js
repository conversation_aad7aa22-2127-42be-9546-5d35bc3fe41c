import express from "express";
const router = express.Router();

// Mock data
const mockData = {
    success: true,
    message: "Mock data retrieved successfully!",
    data: [
        {
            enum_target: "QTY_QR",
            enum_string: "Jumlah QR",
        },
        {
            enum_target: "PRICE_NOMINAL",
            enum_string: "Nominal (Rupiah)",
        },
        {
            enum_target: "VOLUME",
            enum_string: "Volume/Tonase (L/Kg)",
        },
    ],
};

// Define API route
router.get("/", (req, res) => {
    res.json(mockData);
});

export default router;
