import express from "express";
const router = express.Router();

// Mock data
const mockData = {
    success: true,
    message: "Mock data retrieved successfully!",
    data: [
        {
            enum_level: "LEVEL_1",
            string_level: "1 Tingkat",
            index: 1,
        },
        {
            enum_level: "LEVEL_2",
            string_level: "2 Tingkat",
            index: 2,
        },
        {
            enum_level: "LEVEL_3",
            string_level: "3 Tingkat",
            index: 3,
        },
    ],
};

// Define API route
router.get("/", (req, res) => {
    res.json(mockData);
});

export default router;
