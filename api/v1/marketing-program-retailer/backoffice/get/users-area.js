import express from "express";

const router = express.Router();

// Mock data
const mockData = {
    success: true,
    message: "Mock data retrieved successfully!",
    data: [
        {
            id: "06703e0f-c703-41b3-af0f-2b662cec5d47",
            name: "AREA PUSAT",
        },
        {
            id: "0e206bfd-3e87-4f05-a5e2-4a885d141474",
            name: "Area-001",
        },
        {
            id: "1168c9b8-65c6-4207-a2ea-2f6906c602cb",
            name: "Alam Area",
        },
        {
            id: "136f0518-7026-4587-a181-b9b9358188f0",
            name: "BARUWW ASSIGN DARI USER",
        },
        {
            id: "1ba14c90-ebb3-4595-ace8-9196faeca653",
            name: "<PERSON>ate<PERSON> Tim<PERSON>",
        },
        {
            id: "1ec98bc9-c067-439b-adc8-5251a028187c",
            name: "BARUW ASSIGN DARI AREA",
        },
        {
            id: "20447b63-8ea9-41b9-8595-67276b7a98ee",
            name: "PM Area",
        },
        {
            id: "33a1d0bb-6484-4c67-bbec-17f25546556a",
            name: "SUMBAGUT",
        },
        {
            id: "417e4310-17e6-47f2-9b37-7c0cc56f3d7c",
            name: "Area Test",
        },
        {
            id: "4afaae4f-22c9-4c37-9ed4-da8625e94316",
            name: "Jawa Right",
        },
        {
            id: "4f1ee1bd-c82d-4461-83ba-9dd0e2c4ff70",
            name: "AREA 5000",
        },
        {
            id: "60ff4e8d-cb45-4079-8243-c9c4c08b0b8d",
            name: "JATENG",
        },
        {
            id: "6a823233-5f48-4d13-baf5-f85c9ebeba60",
            name: "coba area atha",
        },
        {
            id: "6d8b2619-6a06-4a57-8e20-635c4f13d01c",
            name: "Baruw",
        },
        {
            id: "76021e33-eda5-4fb3-8d56-1acfb26e0dc4",
            name: "jjj",
        },
        {
            id: "7ee0ea2a-12ad-4229-ae16-498ac9ec7a48",
            name: "Area 03",
        },
        {
            id: "83df5466-9e93-4808-a478-733d0bcf721c",
            name: "Area ATHA PM",
        },
        {
            id: "84f15ac8-0fdb-44d4-a385-24b1d271e234",
            name: "Area 002",
        },
        {
            id: "89a52e9c-0182-4e32-a2f0-095c493c2834",
            name: "SULTANBATARA",
        },
        {
            id: "8c4d31e1-13b4-4e4f-9e4c-051f8e3f49be",
            name: "jj",
        },
        {
            id: "9159afd3-988b-42a1-a751-fe44678688a0",
            name: "Ney Area",
        },
        {
            id: "919a5e4c-fb76-4a77-b425-909947802e3e",
            name: "aasa",
        },
        {
            id: "964bf1c4-3430-490a-9740-b7812d6c8e14",
            name: "AREA 1",
        },
        {
            id: "9e4fa819-7679-4146-a345-07b8b6bcf1fc",
            name: "KALIMANTAN",
        },
        {
            id: "9e727346-7b01-4edb-a313-49129463f999",
            name: "jj",
        },
        {
            id: "a2f392cf-b4f0-4cc3-9f79-614d85e40d1b",
            name: "Area Jombang",
        },
        {
            id: "ab36d17a-54ea-4d11-9ba6-7a7943ac6fe2",
            name: "Jamban",
        },
        {
            id: "abb234bd-1367-4e33-9f44-3527e772f1cc",
            name: "BARUWW UNTUK MULTI AREA",
        },
        {
            id: "afc36d8e-3967-48dc-bf3a-36d824f24f9f",
            name: "SUMBAGSEL",
        },
        {
            id: "b2d940f0-b8ff-40a3-a61f-a09c564f3823",
            name: "BALI NUSRA",
        },
        {
            id: "b49c9960-d335-478a-a932-9a3cc7cc2634",
            name: "Nelsy Area",
        },
        {
            id: "bca56f20-3f19-4cea-8914-f599a7e763b9",
            name: "New ",
        },
        {
            id: "c46df9a7-2ad5-441f-bd46-f85d44d5466f",
            name: "NN Area",
        },
        {
            id: "c9df3e11-cbb8-40aa-a565-4b2c144d9d04",
            name: "jj",
        },
        {
            id: "cb68d108-d6ee-422f-ba38-fc395757d70a",
            name: "Jakarta",
        },
        {
            id: "cfb7fc6e-d050-4f6c-8858-9ba76a39002f",
            name: "JATIM",
        },
        {
            id: "d6944990-f65b-4c8b-8acd-3e373fdc68bb",
            name: "Selatan",
        },
        {
            id: "d9ed72f0-56a2-41b1-b424-3474be8992e1",
            name: "JABAR",
        },
        {
            id: "da107460-b1f3-4ec7-8657-d45df164d86e",
            name: "Berau Baru",
        },
        {
            id: "de849e0f-7214-40a0-8c75-fe3d8f7cd017",
            name: "505",
        },
        {
            id: "f317f5a8-23de-4bd4-be91-84f3798bff7b",
            name: "Area Lamongan",
        },
        {
            id: "f611f432-d472-4914-8ee8-f63e3d0d8a04",
            name: "Nelsy Nelly",
        },
        {
            id: "f9c9c957-f848-4963-ad36-c65cc8209c21",
            name: "Jawa Pusat",
        },
        {
            id: "f9f7e000-d3aa-4cf5-b506-7b8140f3be4c",
            name: "SULTENGGO",
        },
    ],
};

// Define API route
router.get("/", (req, res) => {
    res.json(mockData);
});

export default router;
