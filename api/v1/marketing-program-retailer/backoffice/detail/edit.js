import express from "express";
const router = express.Router();

// detail data helper
const _documentData = [
    {
        name: "test.png",
        url: "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_029693ad-d9e1-42f7-8799-f6843f065c6c",
    },
];
const _multilevelTargetProduct = [
    {
        enum_level: "LEVEL_1",
        products: [
            {
                brand_id: "99ec3cea-0131-4308-8ae7-b1d918bff692",
                brand_name: "MAXXFOSATE 490 SL (BRAND)",
                value: 100,
                variant_id: null,
                variant_name: null,
            },
            {
                variant_id: "28f91986-b6c2-42f8-b846-d0fc996b6c51",
                variant_name: "PAKET JAGUNGKLIN SEDANG",
                value: 100,
                brand_id: null,
                brand_name: null,
            },
        ],
        value: 200, // accumulation
    },
    {
        enum_level: "LEVEL_2",
        products: [
            {
                brand_id: "99ec3cea-0131-4308-8ae7-b1d918bff692",
                brand_name: "MAXXFOSATE 490 SL (BRAND)",
                value: 200,
                variant_id: null,
                variant_name: null,
            },
            {
                variant_id: "28f91986-b6c2-42f8-b846-d0fc996b6c51",
                variant_name: "PAKET JAGUNGKLIN SEDANG",
                value: 200,
                brand_id: null,
                brand_name: null,
            },
        ],
        value: 400, // accumulation
    },
    {
        enum_level: "LEVEL_3",
        products: [
            {
                brand_id: "99ec3cea-0131-4308-8ae7-b1d918bff692",
                brand_name: "MAXXFOSATE 490 SL (BRAND)",
                value: 300,
                variant_id: null,
                variant_name: null,
            },
            {
                variant_id: "28f91986-b6c2-42f8-b846-d0fc996b6c51",
                variant_name: "PAKET JAGUNGKLIN SEDANG",
                value: 300,
                brand_id: null,
                brand_name: null,
            },
        ],
        value: 600, // accumulation
    },
];
const _multilevelTargetReward = [
    {
        enum_level: "LEVEL_1",
        reward_type_enum: "MAI_PRODUCT",
        mai_product: {
            variant_id: "a2c712db-53bc-4f2c-a4e5-b3ed44bfafa9",
            variant_name: "ABAPRO 36 EC 500 ML - 40BTL",
            qty: 2,
            maximum_budget: 5000000,
        },
        non_mai_product: null,
    },
    {
        enum_level: "LEVEL_2",
        reward_type_enum: "NON_MAI_PRODUCT",
        non_mai_product: {
            product_other_reward: "TCL 65 4K QLED Google TV",
            maximum_budget: 10000000,
            reward_url:
                "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
        },
        mai_product: null,
    },
    {
        enum_level: "LEVEL_3",
        reward_type_enum: "NON_MAI_PRODUCT",
        non_mai_product: {
            product_other_reward:
                "Kulkas 4 Pintu Samsung RF48 RF48A4000B4SE Multi Door with Twin Cooling",
            maximum_budget: 10000000,
            reward_url:
                "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
        },
        mai_product: null,
    },
];

const _sectionInformation = {
    program_name: "Program Penjualan Retailer Jan-April 2025",
    period_start: 1735664400000,
    period_end: 1745946000000,
    management_note: null,
    reference_number: null,
    document_url: _documentData,
    revision_note: null,
};
const _sectionProgramTerm = {
    is_combined: false,
    scope: {
        scope_enum: "AREA",
        area_ids: [
            "cfb7fc6e-d050-4f6c-8858-9ba76a39002f",
            "60ff4e8d-cb45-4079-8243-c9c4c08b0b8d",
            "d9ed72f0-56a2-41b1-b424-3474be8992e1",
        ], // JATIM,JATENG,JABAR
        subarea_ids: null,
        retailer_ids: null,
    },
    product_origin: {
        scope_enum: "NATIONAL",
        area_ids: null,
        subarea_ids: null,
        distributor_ids: null,
    },
    revision_note: null,
};
const _sectionProductScanTerm = {
    rule_scan: "ACCUMULATION_PRODUCTS",
    target_program: null,
    target_scan_qr: [],
    product_scan: [
        {
            variant_id: "28f91986-b6c2-42f8-b846-d0fc996b6c51",
            variant_name: "PAKET JAGUNGKLIN SEDANG",
            conversion: [
                {
                    variant_id: "6a383156-c41b-4128-89f2-66da95fb7e91",
                    variant_name: "PAKET JAGUNGKLIN KECIL",
                    qty_origin: 1,
                    qty_conversion: 2,
                },
                {
                    variant_id: "f5c89fa1-a843-4dd7-b661-8cf5d1b16e7b",
                    variant_name: "PAKET JAGUNGKLIN BESAR",
                    qty_origin: 3,
                    qty_conversion: 1,
                },
            ],
            brand_id: null,
            brand_name: null,
        },
        {
            brand_id: "99ec3cea-0131-4308-8ae7-b1d918bff692",
            brand_name: "MAXXFOSATE 490 SL (BRAND)",
            conversion: null,
            variant_id: null,
            variant_name: null,
        },
    ],
    multilevel_target: _multilevelTargetProduct,
    revision_note: "",
};
const _sectionRewardTerm = {
    multiplication_enum: "NOT_MULTIPLES",
    multilevel_reward: _multilevelTargetReward,
    reward: null, // single target reward
};
const _sectionTargetScanTerm = {
    rule_target: { is_multilevel_target: true, enum_level: "LEVEL_3" },
    type_target: "QTY_QR",
    revision_note: null,
};

// detail data mock
const _mockSubmitted = {
    status_enum: "SUBMITTED",
    information: {
        ..._sectionInformation,
        reference_number: "123-submitted",
    },
    program_term: _sectionProgramTerm,
    target_scan_term: _sectionTargetScanTerm,
    product_scan_term: _sectionProductScanTerm,
    reward_term: _sectionRewardTerm,
};

const _mockNeedChanged = {
    status_enum: "NEED_CHANGED",
    information: {
        ..._sectionInformation,
        reference_number: "123-need-changed",
    },
    program_term: _sectionProgramTerm,
    target_scan_term: {
        ..._sectionTargetScanTerm,
        revision_note: "Target scan belum mencukupi untuk ketentuan hadiah",
    },
    product_scan_term: _sectionProductScanTerm,
    reward_term: _sectionRewardTerm,
};

const _mockActive = {};

const _mockScheduled = {
    status_enum: "SCHEDULED",
    information: {
        ..._sectionInformation,
        reference_number: "123-scheduled",
    },
    program_term: _sectionProgramTerm,
    target_scan_term: _sectionTargetScanTerm,
    product_scan_term: _sectionProductScanTerm,
    reward_term: _sectionRewardTerm,
};

const _mockCanceled = {};
const _mockFinished = {};

// Mock data
const mockData = {
    success: true,
    message: "Mock data retrieved successfully!",
    data: [_mockSubmitted, _mockNeedChanged, _mockScheduled],
};

// Define API route
router.get("/:id", (req, res) => {
    const { id } = req.params;

    const item = mockData.data.find(
        (entry) => entry.information.reference_number === id
    );

    if (!item) {
        return res.status(404).json({
            success: false,
            message: `No data found for id: ${id}`,
            data: null,
        });
    }

    res.json({
        success: mockData.success,
        message: mockData.message,
        data: item,
    });
});

export default router;
