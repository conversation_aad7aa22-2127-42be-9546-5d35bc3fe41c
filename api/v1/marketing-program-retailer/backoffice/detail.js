import express from "express";
const router = express.Router();

// Mock data
const _detailSubmitted = {
    header: {
        name: "Program Penjualan Retailer Jan-April 2025",
        status_enum: "SUBMITTED",
        status_string: "Diajukan",
        created_changes: {
            actor: "Cahyaning <PERSON>ta Sari",
            date: 1730961423000,
        },
        approved_changes: {
            actor: null,
            date: null,
        },
    },
    information: {
        period_start: 1741651200000,
        period_end: 1743379200000,
        management_note: null,
        reference_number: "001/MAI/CENTRAL/PROG/2024",
        revision_note: "",
        status_verified_enum: null,
    },
    program_term: {
        scope_enum: "AREA",
        scope_string: "JATIM, JATENG, JABAR",
        is_combined: false,
        product_origin_enum: "NATIONAL",
        product_origin_string: "Nasional",
        revision_note: "",
        status_verified_enum: null,
    },
    target_scan_term: {
        rule_target: "Target Bertingkat (3 Tingkat)",
        type_target: "Jumlah QR",
        revision_note: "",
        status_verified_enum: null,
    },
    product_scan_term: {
        rule_scan_enum: "",
        product_scan: ["MAXXFOSATE 490 SL (BRAND)", "PAKET JAGUNGKLIN SEDANG"],
        has_conversion: true,
        is_multilevel_target: true,
        targets: [
            {
                level_string: "Target Tingkat 1",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (100 QR)",
                    "PAKET JAGUNGKLIN SEDANG (100 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 2",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (200 QR)",
                    "PAKET JAGUNGKLIN SEDANG (200 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 3",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (300 QR)",
                    "PAKET JAGUNGKLIN SEDANG (300 QR)",
                ],
            },
        ],
        conversions: [
            {
                variant_name: "PAKET JAGUNGKLIN SEDANG",
                variant_conversion: [
                    {
                        origin_variant: "1 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "2 QR PAKET JAGUNGKLIN KECIL",
                    },
                    {
                        origin_variant: "3 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "1 QR PAKET JAGUNGKLIN BESAR",
                    },
                ],
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    reward_term: {
        multiplication_string: "Tidak Berlaku Kelipatan",
        reward_list: [
            {
                reward_type_enum: "MAI_PRODUCT",
                non_mai_product: null,
                mai_product: {
                    variant_name: ": CYCLON 290SL 800 ML",
                    qty: 2,
                    sale_unit: "BOX",
                    maximum_budget: 5000000.0,
                },
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward: "TCL 65 4K QLED Google TV",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward:
                        "Kulkas 4 Pintu Samsung RF48 RF48A4000B4SE Multi Door with Twin Cooling",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    document_url: [
        {
            name: "test.png",
            url: "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_029693ad-d9e1-42f7-8799-f6843f065c6c",
        },
    ],
    cancel_reason: null,
    finished_by_system: {
        date: null,
        reason: [""],
    },
};

const _detailActive = {
    header: {
        name: "Program Penjualan Retailer Jan-April 2025",
        status_enum: "ACTIVE",
        status_string: "Aktif",
        created_changes: {
            actor: "Cahyaning Novita Sari",
            date: 1730961423000,
        },
        approved_changes: {
            actor: "SUGIONO HADINOTO",
            date: 1731029004000,
        },
    },
    information: {
        period_start: 1743379200000,
        period_end: 1743379200000,
        management_note: null,
        reference_number: "001/MAI/CENTRAL/PROG/2024",
        revision_note: "",
        status_verified_enum: null,
    },
    program_term: {
        scope_enum: "AREA",
        scope_string: "JATIM, JATENG, JABAR",
        is_combined: false,
        product_origin_enum: "NATIONAL",
        product_origin_string: "Nasional",
        revision_note: "",
        status_verified_enum: null,
    },
    target_scan_term: {
        rule_target: "Target Bertingkat (3 Tingkat)",
        type_target: "Jumlah QR",
        revision_note: "",
        status_verified_enum: null,
    },
    product_scan_term: {
        rule_scan_enum: "",
        product_scan: ["MAXXFOSATE 490 SL (BRAND)", "PAKET JAGUNGKLIN SEDANG"],
        has_conversion: true,
        is_multilevel_target: true,
        targets: [
            {
                level_string: "Target Tingkat 1",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (100 QR)",
                    "PAKET JAGUNGKLIN SEDANG (100 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 2",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (200 QR)",
                    "PAKET JAGUNGKLIN SEDANG (200 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 3",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (300 QR)",
                    "PAKET JAGUNGKLIN SEDANG (300 QR)",
                ],
            },
        ],
        conversions: [
            {
                variant_name: "PAKET JAGUNGKLIN SEDANG",
                variant_conversion: [
                    {
                        origin_variant: "1 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "2 QR PAKET JAGUNGKLIN KECIL",
                    },
                    {
                        origin_variant: "3 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "1 QR PAKET JAGUNGKLIN BESAR",
                    },
                ],
            },
            {
                variant_name: "PAKET JAGUNGKLIN BERAT",
                variant_conversion: [
                    {
                        origin_variant: "1 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "2 QR PAKET JAGUNGKLIN KECIL",
                    },
                    {
                        origin_variant: "3 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "1 QR PAKET JAGUNGKLIN BESAR",
                    },
                ],
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    reward_term: {
        multiplication_string: "Tidak Berlaku Kelipatan",
        reward_list: [
            {
                reward_type_enum: "MAI_PRODUCT",
                non_mai_product: null,
                mai_product: {
                    variant_name: ": CYCLON 290SL 800 ML",
                    qty: 2,
                    sale_unit: "BOX",
                    maximum_budget: 5000000.0,
                },
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward: "TCL 65 4K QLED Google TV",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward:
                        "Kulkas 4 Pintu Samsung RF48 RF48A4000B4SE Multi Door with Twin Cooling",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    document_url: [
        {
            name: "test.png",
            url: "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_029693ad-d9e1-42f7-8799-f6843f065c6c",
        },
    ],
    cancel_reason: null,
    finished_by_system: {
        date: null,
        reason: [""],
    },
};

const _detailNeedChanged = {
    header: {
        name: "Program Penjualan Retailer Jan-April 2025",
        status_enum: "NEED_CHANGED",
        status_string: "Butuh Perbaikan",
        created_changes: {
            actor: "Cahyaning Novita Sari",
            date: 1730961423000,
        },
        approved_changes: {
            actor: null,
            date: null,
        },
    },
    information: {
        period_start: 1735664400000,
        period_end: 1745946000000,
        management_note: null,
        reference_number: "mock-active",
        revision_note: "",
        status_verified_enum: null,
    },
    program_term: {
        scope_enum: "AREA",
        scope_string: "JATIM, JATENG, JABAR",
        is_combined: false,
        product_origin_enum: "NATIONAL",
        product_origin_string: "Nasional",
        revision_note: "",
        status_verified_enum: null,
    },
    target_scan_term: {
        rule_target: "Target Bertingkat (3 Tingkat)",
        type_target: "Jumlah QR",
        revision_note: "Target scan belum mencukupi untuk ketentuan hadiah",
        status_verified_enum: null,
    },
    product_scan_term: {
        rule_scan_enum: "",
        product_scan: ["MAXXFOSATE 490 SL (BRAND)", "PAKET JAGUNGKLIN SEDANG"],
        has_conversion: true,
        is_multilevel_target: true,
        targets: [
            {
                level_string: "Target Tingkat 1",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (100 QR)",
                    "PAKET JAGUNGKLIN SEDANG (100 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 2",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (200 QR)",
                    "PAKET JAGUNGKLIN SEDANG (200 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 3",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (300 QR)",
                    "PAKET JAGUNGKLIN SEDANG (300 QR)",
                ],
            },
        ],
        conversions: [
            {
                variant_name: "PAKET JAGUNGKLIN SEDANG",
                variant_conversion: [
                    {
                        origin_variant: "1 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "2 QR PAKET JAGUNGKLIN KECIL",
                    },
                    {
                        origin_variant: "3 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "1 QR PAKET JAGUNGKLIN BESAR",
                    },
                ],
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    reward_term: {
        multiplication_string: "Tidak Berlaku Kelipatan",
        reward_list: [
            {
                reward_type_enum: "MAI_PRODUCT",
                non_mai_product: null,
                mai_product: {
                    variant_name: ": CYCLON 290SL 800 ML",
                    qty: 2,
                    sale_unit: "BOX",
                    maximum_budget: 5000000.0,
                },
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward: "TCL 65 4K QLED Google TV",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward:
                        "Kulkas 4 Pintu Samsung RF48 RF48A4000B4SE Multi Door with Twin Cooling",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    document_url: [
        {
            name: "test.png",
            url: "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_029693ad-d9e1-42f7-8799-f6843f065c6c",
        },
    ],
    cancel_reason: null,
    finished_by_system: {
        date: null,
        reason: [""],
    },
};

const _detailScheduled = {
    header: {
        name: "Program Penjualan Retailer Jan-April 2025",
        status_enum: "SCHEDULED",
        status_string: "Dijadwalkan",
        created_changes: {
            actor: "Cahyaning Novita Sari",
            date: 1730961423000,
        },
        approved_changes: {
            actor: "SUGIONO HADINOTO",
            date: 1762590204000,
        },
    },
    information: {
        period_start: 1735664400000,
        period_end: 1745946000000,
        management_note: null,
        reference_number: "001/MAI/CENTRAL/PROG/2024",
        revision_note: "",
        status_verified_enum: null,
    },
    program_term: {
        scope_enum: "AREA",
        scope_string: "JATIM, JATENG, JABAR",
        is_combined: false,
        product_origin_enum: "NATIONAL",
        product_origin_string: "Nasional",
        revision_note: "",
        status_verified_enum: null,
    },
    target_scan_term: {
        rule_target: "Target Bertingkat (3 Tingkat)",
        type_target: "Jumlah QR",
        revision_note: "",
        status_verified_enum: null,
    },
    product_scan_term: {
        rule_scan_enum: "",
        product_scan: ["MAXXFOSATE 490 SL (BRAND)", "PAKET JAGUNGKLIN SEDANG"],
        has_conversion: true,
        is_multilevel_target: true,
        targets: [
            {
                level_string: "Target Tingkat 1",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (100 QR)",
                    "PAKET JAGUNGKLIN SEDANG (100 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 2",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (200 QR)",
                    "PAKET JAGUNGKLIN SEDANG (200 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 3",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (300 QR)",
                    "PAKET JAGUNGKLIN SEDANG (300 QR)",
                ],
            },
        ],
        conversions: [
            {
                variant_name: "PAKET JAGUNGKLIN SEDANG",
                variant_conversion: [
                    {
                        origin_variant: "1 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "2 QR PAKET JAGUNGKLIN KECIL",
                    },
                    {
                        origin_variant: "3 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "1 QR PAKET JAGUNGKLIN BESAR",
                    },
                ],
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    reward_term: {
        multiplication_string: "Tidak Berlaku Kelipatan",
        reward_list: [
            {
                reward_type_enum: "MAI_PRODUCT",
                non_mai_product: null,
                mai_product: {
                    variant_name: "CYCLON 290SL 800 ML",
                    qty: 2,
                    sale_unit: "BOX",
                    maximum_budget: 5000000.0,
                },
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward: "TCL 65 4K QLED Google TV",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward:
                        "Kulkas 4 Pintu Samsung RF48 RF48A4000B4SE Multi Door with Twin Cooling",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    document_url: [
        {
            name: "test.png",
            url: "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_029693ad-d9e1-42f7-8799-f6843f065c6c",
        },
    ],
    cancel_reason: null,
    finished_by_system: {
        date: null,
        reason: [""],
    },
};

const _detailCanceled = {
    header: {
        name: "Program Penjualan Retailer Jan-April 2025",
        status_enum: "CANCELED",
        status_string: "Dibatalkan",
        created_changes: {
            actor: "Cahyaning Novita Sari",
            date: 1730961423000,
        },
        approved_changes: {
            actor: "SUGIONO HADINOTO",
            date: 1762590204000,
        },
    },
    information: {
        period_start: 1735664400000,
        period_end: 1745946000000,
        management_note: null,
        reference_number: "001/MAI/CENTRAL/PROG/2024",
        revision_note: "",
        status_verified_enum: null,
    },
    program_term: {
        scope_enum: "AREA",
        scope_string: "JATIM, JATENG, JABAR",
        is_combined: false,
        product_origin_enum: "NATIONAL",
        product_origin_string: "Nasional",
        revision_note: "",
        status_verified_enum: null,
    },
    target_scan_term: {
        rule_target: "Target Bertingkat (3 Tingkat)",
        type_target: "Jumlah QR",
        revision_note: "",
        status_verified_enum: null,
    },
    product_scan_term: {
        rule_scan_enum: "",
        product_scan: ["MAXXFOSATE 490 SL (BRAND)", "PAKET JAGUNGKLIN SEDANG"],
        has_conversion: true,
        is_multilevel_target: true,
        targets: [
            {
                level_string: "Target Tingkat 1",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (100 QR)",
                    "PAKET JAGUNGKLIN SEDANG (100 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 2",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (200 QR)",
                    "PAKET JAGUNGKLIN SEDANG (200 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 3",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (300 QR)",
                    "PAKET JAGUNGKLIN SEDANG (300 QR)",
                ],
            },
        ],
        conversions: [
            {
                variant_name: "PAKET JAGUNGKLIN SEDANG",
                variant_conversion: [
                    {
                        origin_variant: "1 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "2 QR PAKET JAGUNGKLIN KECIL",
                    },
                    {
                        origin_variant: "3 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "1 QR PAKET JAGUNGKLIN BESAR",
                    },
                ],
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    reward_term: {
        multiplication_string: "Tidak Berlaku Kelipatan",
        reward_list: [
            {
                reward_type_enum: "MAI_PRODUCT",
                non_mai_product: null,
                mai_product: {
                    variant_name: ": CYCLON 290SL 800 ML",
                    qty: 2,
                    sale_unit: "BOX",
                    maximum_budget: 5000000.0,
                },
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward: "TCL 65 4K QLED Google TV",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward:
                        "Kulkas 4 Pintu Samsung RF48 RF48A4000B4SE Multi Door with Twin Cooling",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    document_url: [
        {
            name: "test.png",
            url: "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_029693ad-d9e1-42f7-8799-f6843f065c6c",
        },
    ],
    cancel_reason: "Memperbarui produk & ketentuan program marketing",
    finished_by_system: {
        date: null,
        reason: [""],
    },
};

const _detailFinished = {
    header: {
        name: "Program Penjualan Retailer Jan-April 2025",
        status_enum: "FINISHED",
        status_string: "Selesai",
        created_changes: {
            actor: "Cahyaning Novita Sari",
            date: 1730961423000,
        },
        approved_changes: {
            actor: "SUGIONO HADINOTO",
            date: 1762590204000,
        },
    },
    information: {
        period_start: 1735664400000,
        period_end: 1745946000000,
        management_note: null,
        reference_number: "001/MAI/CENTRAL/PROG/2024",
        revision_note: "",
        status_verified_enum: null,
    },
    program_term: {
        scope_enum: "AREA",
        scope_string: "JATIM, JATENG, JABAR",
        is_combined: false,
        product_origin_enum: "NATIONAL",
        product_origin_string: "Nasional",
        revision_note: "",
        status_verified_enum: null,
    },
    target_scan_term: {
        rule_target: "Target Bertingkat (3 Tingkat)",
        type_target: "Jumlah QR",
        revision_note: "",
        status_verified_enum: null,
    },
    product_scan_term: {
        rule_scan_enum: "",
        product_scan: ["MAXXFOSATE 490 SL (BRAND)", "PAKET JAGUNGKLIN SEDANG"],
        has_conversion: true,
        is_multilevel_target: true,
        targets: [
            {
                level_string: "Target Tingkat 1",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (100 QR)",
                    "PAKET JAGUNGKLIN SEDANG (100 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 2",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (200 QR)",
                    "PAKET JAGUNGKLIN SEDANG (200 QR)",
                ],
            },
            {
                level_string: "Target Tingkat 3",
                list_product: [
                    "MAXXFOSATE 490 SL (BRAND) (300 QR)",
                    "PAKET JAGUNGKLIN SEDANG (300 QR)",
                ],
            },
        ],
        conversions: [
            {
                variant_name: "PAKET JAGUNGKLIN SEDANG",
                variant_conversion: [
                    {
                        origin_variant: "1 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "2 QR PAKET JAGUNGKLIN KECIL",
                    },
                    {
                        origin_variant: "3 QR PAKET JAGUNGKLIN SEDANG",
                        conversion_variant: "1 QR PAKET JAGUNGKLIN BESAR",
                    },
                ],
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    reward_term: {
        multiplication_string: "Tidak Berlaku Kelipatan",
        reward_list: [
            {
                reward_type_enum: "MAI_PRODUCT",
                non_mai_product: null,
                mai_product: {
                    variant_name: ": CYCLON 290SL 800 ML",
                    qty: 2,
                    sale_unit: "BOX",
                    maximum_budget: 5000000.0,
                },
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward: "TCL 65 4K QLED Google TV",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
            {
                reward_type_enum: "NON_MAI_PRODUCT",
                non_mai_product: {
                    product_other_reward:
                        "Kulkas 4 Pintu Samsung RF48 RF48A4000B4SE Multi Door with Twin Cooling",
                    maximum_budget: 10000000.0,
                    reward_pic_url:
                        "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_3620a9dd-f512-43ef-a35c-2bd5df0a4fc6",
                },
                mai_product: null,
            },
        ],
        revision_note: "",
        status_verified_enum: null,
    },
    document_url: [
        {
            name: "test.png",
            url: "https://s3-ap-southeast-1.amazonaws.com/maxxi-staging/image/user/IMAGE_029693ad-d9e1-42f7-8799-f6843f065c6c",
        },
    ],
    cancel_reason: null,
    finished_by_system: {
        date: null,
        reason: [
            "Program telah mencapai target & budget marketing hampir over.",
        ],
    },
};

const mockData = {
    success: true,
    message: "Mock data retrieved successfully!",
    data: null,
};

// Define API route
router.get("/:id", (req, res) => {
    const { id } = req.params;

    switch (id) {
        case "123-submitted":
            mockData.data = _detailSubmitted;
            break;
        case "123-active":
            mockData.data = _detailActive;
            break;
        case "123-need-changed":
            mockData.data = _detailNeedChanged;
            break;

        case "123-scheduled":
            mockData.data = _detailScheduled;
            break;

        case "123-canceled":
            mockData.data = _detailCanceled;
            break;
        case "123-finished":
            mockData.data = _detailFinished;
            break;
    }

    if (!mockData.data) {
        return res.status(404).json({
            success: false,
            message: `No data found for program id: ${id}`,
            data: null,
        });
    }

    res.json({
        success: mockData.success,
        message: mockData.message,
        data: mockData.data,
    });
});

export default router;
