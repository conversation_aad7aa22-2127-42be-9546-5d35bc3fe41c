import express from "express";
const router = express.Router();

// Mock data
const mockData = {
    success: true,
    message: "Mock data retrieved successfully!",
    data: [
        {
            id: "38d819e4-7200-11ee-a1d4-0a011f33cfb2-sample",
            name: "AKTIV 100 ML - 100 BTL",
            delivery_unit: "LT",
        },
        {
            id: "38d819e4-7200-11ee-a1d4-0a011f33cfb2-sample2",
            name: "AKTIV 250 ML - 100 BTL",
            delivery_unit: "LT",
        },
        {
            id: "38d819e4-7200-11ee-a1d4-0a011f33cfb2-sample3",
            name: "AKTIV NEW 100 ML - 100 BTL",
            delivery_unit: "LT",
        },
        {
            id: "38d819e4-7200-11ee-a1d4-0a011f33cfb2-sample4",
            name: "AKTIV NEW 250 ML - 100 BTL",
            delivery_unit: "LT",
        },
        {
            id: "dummy-variant-bufos-ceban",
            name: "BUFOS 100SL 10,000 ML",
            delivery_unit: "LT",
        },
        {
            id: "dummy-variant-bufos-noceng",
            name: "BUFOS 100SL 2,000 ML",
            delivery_unit: "LT",
        },
        {
            id: "6a383156-c41b-4128-89f2-66da95fb7e91",
            name: "PAKET JAGUNGKLIN KECIL",
            delivery_unit: "LT",
        },
        {
            id: "f5c89fa1-a843-4dd7-b661-8cf5d1b16e7b",
            name: "PAKET JAGUNGKLIN BESAR",
            delivery_unit: "LT",
        },
    ],
};

// Define API route
router.get("/:id", (req, res) => {
    const { id } = req.params;
    res.json(mockData);
});

export default router;
