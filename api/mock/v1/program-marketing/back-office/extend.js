import express from "express";
const router = express.Router();

// Mock data
const mockData = {
  success: true,
  message: "Mock data retrieved successfully!",
  data: []
};

// Define API route
router.post("/:id", (req, res) => {
  const { id } = req.params;
  const { periode_end, document_url } = req.body;

  if (!id) {
    return res.status(400).json({
      success: false,
      message: 'Program ID is required',
      data: null
    });
  }

  mockData.message = "Periode Program Marketing berhasil diperbarui dan sedang menunggu verifikasi Finance"
  res.json(mockData);
});

export default router;
