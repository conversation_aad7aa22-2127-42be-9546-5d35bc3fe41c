import express from "express";
const router = express.Router();

// Valid enum values
const VALID_TYPES = [
  "INFORMATION",
  "PROGRAM_TERM",
  "ORDER_TERM",
  "SCHEMA_PROMOTIONS"
];

const VALID_STATUSES = [
  null,
  "VERIFIED",
  "REQUEST_REVISION"
];

// Define API route for updating status
router.put("/:id", (req, res) => {
  const { id } = req.params;
  const { type, status } = req.body;

  // Validation
  if (!type) {
    return res.status(400).json({
      success: false,
      message: "Type is required",
      data: null
    });
  }

  if (!VALID_TYPES.includes(type)) {
    return res.status(400).json({
      success: false,
      message: `Invalid type. Valid types are: ${VALID_TYPES.join(", ")}`,
      data: null
    });
  }

  if (!VALID_STATUSES.includes(status)) {
    return res.status(400).json({
      success: false,
      message: `Invalid status. Valid statuses are: ${VALID_STATUSES.filter(s => s !== null).join(", ")}, or null`,
      data: null
    });
  }

  // Mock successful response
  const responseData = {
    program_marketing_id: id,
    type: type,
    status: status
  };

  res.json({
    success: true,
    message: "Status updated successfully!",
    data: responseData
  });
});

export default router;
