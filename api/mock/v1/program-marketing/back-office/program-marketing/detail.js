import express from "express";
const router = express.Router();

// Mock data
// detail discount purchase
const _detailDiscountPurchaseSubmitted = {
  header: {
    name: "Program Paket",
    status_program_marketing_enum: "SUBMITTED",
    status_program_marketing_string: "Diajukan",
    created: {
      actor_name: "<PERSON>ahyaning <PERSON>ta Sari",
      date: 1730912400000
    },
    modified: null,
    finance_approved: null
  },
  information: {
    status_verified_enum: null,
    program_type_enum: "DISCOUNT_PURCHASE",
    program_type_string: "Diskon Pembelian",
    period_start: 1751302800000,
    period_end: 1756573200000,
    extend_period_end: null,
    management_note: "",
    reference_number: "001/MAI/CENTRAL/PROG/2025",
    revision_note: null
  },
  program_term: {
    status_verified_enum: null,
    scope_enum: "NATIONAL",
    scope_string: "Nasional",
    selected_area_ids: null,
    selected_sub_area_ids: null,
    selected_distributor_ids: null,
    quota: {
      enum_type: "UNLIMITED",
      max_qty: 0,
      scope_list: null
    },
    long_term_distributor_inclusion: "EXCLUDE_ALL",
    excluded_distributor_program: null,
    retailer_inclusion: "EXCLUDE_PARTIAL",
    excluded_retailer_program: [
      {
        id: "1-abc",
        name: "PROGRAM BENIH JAGUNG"
      },
      {
        id: "2-abc",
        name: "Program Amonium Glufosinate"
      },
      {
        id: "3-abc",
        name: "Program Paket No Gulma January Juni"
      }
    ],
    revision_note: null
  },
  order_term: {
    status_verified_enum: null,
    purchase_order_type_enum: "BUNDLING",
    product_variant: [
      {
        id: "720c2b4e-1a43-441e-a77b-212f7349c464",
        name: "PAKET BUNGLON"
      },
      {
        id: "36c76089-a033-4914-a3c7-6f068bd45650",
        name: "PAKET NEOKING"
      },
      {
        id: "e4fe804b-866f-44d7-afbf-6d1c267a7310",
        name: "PAKET KINGMAXX"
      }
    ],
    minimum_purchase: null,
    revision_note: null
  },
  schema_promotions: [
    {
      id: "0",
      status_verified_enum: null,
      revision_note: null,
      order_product: [
        {
          name: "PAKET BUNGLON",
          qty: "10 box",
          discount: "Fixed Diskon 27,6%"
        },
        {
          name: "PAKET NEOKING",
          qty: "10 box",
          discount: "Maksimal Diskon 20,59%"
        },
        {
          name: "PAKET KINGMAXX",
          qty: "10 box",
          discount: "Maksimal Sales Discount Lv 2"
        }
      ]
    }
  ],
  document_url: [
    {
      url: "https://s3-ap-southeast-1.amazonaws.com/mai-prod/file/FILE_4a5b103f-61c4-4243-bfe1-8988d285415e",
      name: "sample image"
    }
  ]
}

const _detailDiscountPurchaseActive = {
  header: {
    name: "SUMBAGSEL DISKON DEBESTAR ( MINIMAL 100 LITER )",
    status_program_marketing_enum: "ACTIVE",
    status_program_marketing_string: "Aktif",
    created: {
      actor_name: "Bangun Ferdian",
      date: 1745896514819
    },
    modified: {
      actor_name: "Putri Aprilia",
      date: 1753899501178
    },
    finance_approved: {
      actor_name: "Putri Aprilia",
      date: 1746167697396
    }
  },
  information: {
    status_verified_enum: "VERIFIED",
    program_type_enum: "DISCOUNT_PURCHASE",
    program_type_string: "Diskon Pembelian",
    period_start: 1745884800000,
    period_end: 1767139200000,
    extend_period_end: null,
    management_note: "",
    reference_number: "05/MKT-INT/MAI/Sumbagsel/I/2025",
    revision_note: null
  },
  program_term: {
    status_verified_enum: "VERIFIED",
    scope_enum: "AREA",
    scope_string: "SUMBAGSEL",
    selected_area_ids: [
      "afc36d8e-3967-48dc-bf3a-36d824f24f9f"
    ],
    selected_sub_area_ids: null,
    selected_distributor_ids: null,
    quota: {
      enum_type: "UNLIMITED",
      max_qty: 0,
      scope_list: null
    },
    long_term_distributor_inclusion: "EXCLUDE_ALL",
    excluded_distributor_program: null,
    retailer_inclusion: "EXCLUDE_ALL",
    excluded_retailer_program: null,
    revision_note: null
  },
  order_term: {
    status_verified_enum: "VERIFIED",
    purchase_order_type_enum: "ACCUMULATION",
    product_variant: [
      {
        id: "edf94b16-fde3-40b8-b264-ae71104b5ff4",
        name: "DEBESTAR 200/260 SC 100 ML - 100 BTL"
      },
      {
        id: "5c4fb2b0-e8f0-4fab-88d7-b1afd3b4aa46",
        name: "DEBESTAR 200/260 SC 500 ML - 40BTL"
      },
      {
        id: "f51c64eb-a25b-42f4-9fea-c25ddd69c827",
        name: "DEBESTAR 200/260 SC 250 ML"
      }
    ],
    minimum_purchase: "100 LT",
    revision_note: null
  },
  schema_promotions: [
    {
      id: "0",
      status_verified_enum: null,
      revision_note: null,
      order_product: [
        {
          name: "DEBESTAR 200/260 SC 500 ML - 40BTL",
          qty: null,
          discount: "Maksimal Diskon 15%"
        },
        {
          name: "DEBESTAR 200/260 SC 250 ML",
          qty: null,
          discount: "Maksimal Diskon 15%"
        },
        {
          name: "DEBESTAR 200/260 SC 100 ML",
          qty: null,
          discount: "Maksimal Diskon 15%"
        }
      ]
    }
  ],
  document_url: [
    {
      url: "https://s3-ap-southeast-1.amazonaws.com/mai-prod/file/FILE_a06f9202-d1a1-4cf4-90e9-62e1fa4f286a",
      name: "HARGA DEBESTAR SUMBAGSEL.pdf"
    }
  ]
}

const _detailDiscountPurchaseExtendPeriod = {
  header: {
    name: "SUMBAGSEL DISKON DEBESTAR ( MINIMAL 100 LITER )",
    status_program_marketing_enum: "EXTEND_PERIOD",
    status_program_marketing_string: "Pengajuan Perpanjangan",
    created: {
      actor_name: "Bangun Ferdian",
      date: 1745896514819
    },
    modified: {
      actor_name: "Putri Aprilia",
      date: 1753899501178
    },
    finance_approved: {
      actor_name: "Putri Aprilia",
      date: 1746167697396
    }
  },
  information: {
    status_verified_enum: "VERIFIED",
    program_type_enum: "DISCOUNT_PURCHASE",
    program_type_string: "Diskon Pembelian",
    period_start: 1745884800000,
    period_end: 1767139200000,
    extend_period_end: 1769792400000,
    management_note: "",
    reference_number: "05/MKT-INT/MAI/Sumbagsel/I/2025",
    revision_note: null
  },
  program_term: {
    status_verified_enum: "VERIFIED",
    scope_enum: "AREA",
    scope_string: "SUMBAGSEL",
    selected_area_ids: [
      "afc36d8e-3967-48dc-bf3a-36d824f24f9f"
    ],
    selected_sub_area_ids: null,
    selected_distributor_ids: null,
    quota: {
      enum_type: "UNLIMITED",
      max_qty: 0,
      scope_list: null
    },
    long_term_distributor_inclusion: "EXCLUDE_ALL",
    excluded_distributor_program: null,
    retailer_inclusion: "EXCLUDE_ALL",
    excluded_retailer_program: null,
    revision_note: null
  },
  order_term: {
    status_verified_enum: "VERIFIED",
    purchase_order_type_enum: "ACCUMULATION",
    product_variant: [
      {
        id: "edf94b16-fde3-40b8-b264-ae71104b5ff4",
        name: "DEBESTAR 200/260 SC 100 ML - 100 BTL"
      },
      {
        id: "5c4fb2b0-e8f0-4fab-88d7-b1afd3b4aa46",
        name: "DEBESTAR 200/260 SC 500 ML - 40BTL"
      },
      {
        id: "f51c64eb-a25b-42f4-9fea-c25ddd69c827",
        name: "DEBESTAR 200/260 SC 250 ML"
      }
    ],
    minimum_purchase: "100 LT",
    revision_note: null
  },
  schema_promotions: [
    {
      id: "0",
      status_verified_enum: null,
      revision_note: null,
      order_product: [
        {
          name: "DEBESTAR 200/260 SC 500 ML - 40BTL",
          qty: null,
          discount: "Maksimal Diskon 15%"
        },
        {
          name: "DEBESTAR 200/260 SC 250 ML",
          qty: null,
          discount: "Maksimal Diskon 15%"
        },
        {
          name: "DEBESTAR 200/260 SC 100 ML",
          qty: null,
          discount: "Maksimal Diskon 15%"
        }
      ]
    }
  ],
  document_url: [
    {
      url: "https://s3-ap-southeast-1.amazonaws.com/mai-prod/file/FILE_a06f9202-d1a1-4cf4-90e9-62e1fa4f286a",
      name: "HARGA DEBESTAR SUMBAGSEL.pdf"
    }
  ]
}

const _detailDiscountPurchaseNeedChanged = {}

const mockData = {
  success: true,
  message: "Mock data retrieved successfully!",
  data: null,
};

// Define API route
router.get("/:id", (req, res) => {
  const { id } = req.params;

  switch(id) {
    case "123-submitted":
      mockData.data = _detailDiscountPurchaseSubmitted;
      break;
  }

  if (!mockData.data) {
    return res.status(404).json({
      success: false,
      message: `No data found for program id: ${id}`,
      data: null,
    });
  }

  res.json({
    success: mockData.success,
    message: mockData.message,
    data: mockData.data,
  });
});

export default router;
