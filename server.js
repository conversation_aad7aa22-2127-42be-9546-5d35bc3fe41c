import express from "express";
import cors from "cors";
import fs from "fs";
import path from "path";
import { fileURLToPath, pathToFileURL } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = process.env.PORT || 3000;

app.use(cors());
app.use(express.json()); // Add JSON body parser middleware

// Define API directory in a single variable
const API_DIR = path.join(__dirname, "api");

// Test Route
app.get("/test", (req, res) => {
    res.json({ success: true, message: "Server is running!" });
});

// Function to dynamically load all routes from API_DIR
const loadRoutes = async (dir, prefix = "/api") => {
    if (!fs.existsSync(dir)) return;

    for (const file of fs.readdirSync(dir)) {
        const fullPath = path.join(dir, file);
        if (fs.statSync(fullPath).isDirectory()) {
            await loadRoutes(fullPath, `${prefix}/${file}`);
        } else if (file.endsWith(".js")) {
            try {
                const routeModule = await import(pathToFileURL(fullPath).href);
                if (typeof routeModule.default === "function") {
                    const routePath = `${prefix}/${file.replace(".js", "")}`;
                    app.use(routePath, routeModule.default);
                    console.log(`✅ Route registered: ${routePath}`);
                }
            } catch (error) {
                console.error(`❌ Error loading ${fullPath}:`, error);
            }
        }
    }
};

// Load routes using API_DIR
await loadRoutes(API_DIR);

// Export for Vercel
export default app;

// Start server locally
if (process.env.NODE_ENV !== "production") {
    app.listen(port, () =>
        console.log(`🚀 Server running on http://localhost:${port}`)
    );
}
