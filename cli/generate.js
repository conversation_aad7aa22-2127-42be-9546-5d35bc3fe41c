import fs from "fs";
import path from "path";
import inquirer from "inquirer";

const BASE_API_DIR = path.join(process.cwd(), "api");

async function generateAPI() {
    // Ensure /api directory exists
    if (!fs.existsSync(BASE_API_DIR)) {
        fs.mkdirSync(BASE_API_DIR, { recursive: true });
    }

    // Prompt for full API path
    const { fullPathInput } = await inquirer.prompt([
        {
            name: "fullPathInput",
            type: "input",
            message:
                "Enter full API path relative to /api (e.g., 'v1/xx/yy/your-api-name'):",
            validate: (input) => input.trim() !== "" || "Path cannot be empty.",
        },
    ]);

    // Final full file path
    const filePath = path.join(BASE_API_DIR, `${fullPathInput}.js`);
    const folderPath = path.dirname(filePath);

    // Check if file already exists
    if (fs.existsSync(filePath)) {
        console.log(`⚠️ File already exists: ${filePath}`);
        return;
    }

    // Create folders if needed
    if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
    }

    // API Route boilerplate
    const apiRouteCode = `import express from "express";
const router = express.Router();

// Mock data
const mockData = {
  success: true,
  message: "Mock data retrieved successfully!",
  data: []
};

// Define API route
router.get("/", (req, res) => {
  res.json(mockData);
});

export default router;
`;

    // Write file
    fs.writeFileSync(filePath, apiRouteCode, "utf8");

    console.log(`🎉 API file successfully generated at: ${filePath}`);
    console.log("🚀 Ready to use in your mock API setup!");
}

export default generateAPI;
