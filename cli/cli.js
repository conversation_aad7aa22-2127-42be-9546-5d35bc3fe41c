#!/usr/bin/env node

import { fileURLToPath, pathToFileURL } from "url";
import path from "path";
import readline from "readline";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const args = process.argv.slice(2);
const command = args[0];

// Function to handle CLI exit
function exitCLI() {
    console.log("Exiting CLI...");
    process.exit(0);
}

// Set up interactive input handling
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
});

rl.on("SIGINT", () => {
    console.log("\nProcess interrupted. Exiting CLI...");
    process.exit(0);
});

if (command === "generate") {
    import(pathToFileURL(path.join(__dirname, "generate.js")).href)
        .then((module) => module.default())
        .finally(exitCLI);
} else if (command === "exit") {
    exitCLI();
} else {
    console.log("Usage: mockapi <command>");
    console.log("Commands:");
    console.log("  generate   Generate API file");
    console.log("  exit       Exit the CLI");

    // Allow users to type "exit" to quit
    rl.question("\nType 'exit' to quit: ", (answer) => {
        if (answer.toLowerCase() === "exit") {
            exitCLI();
        } else {
            console.log("Invalid input. Exiting CLI...");
            exitCLI();
        }
    });
}
