# Atom MockAPI

Atom MockAPI adalah server mock API berbasis Node.js + Express yang memudahkan pengembangan dan pengujian aplikasi tanpa backend asli. Semua endpoint di-generate otomatis dari struktur folder `/api`, serta dilengkapi CLI untuk mempercepat pembuatan file endpoint mock.

## Fitur Utama
- **Mock API Dinamis:** Semua file JS di `/api` otomatis menjadi endpoint.
- **CLI Otomatis:** Perintah `mockapi generate` untuk membuat file endpoint mock baru.
- **Siap Deploy Vercel:** Konfigurasi sudah siap untuk deployment ke Vercel.
- **CORS Support:** Mendukung cross-origin request.
- **Struktur Modular:** Mudah dikembangkan dan diorganisasi.

## Struktur Folder
```
/project-root
├── api/                # Semua endpoint mock
├── cli/                # CLI utility
│   ├── cli.js          # Entry CLI
│   └── generate.js     # Script generator endpoint
├── server.js           # Entry point Express
├── package.json        # Dependensi & script
├── vercel.json         # Konfigurasi Vercel
└── README.md           # Dokumentasi
```

## Cara Menjalankan
1. **Install dependensi:**
   ```sh
   npm install
   ```
2. **Jalankan server mock secara lokal:**
   ```sh
   npm start
   ```
   Server berjalan di `http://localhost:3000` (atau port dari env).
3. **(Opsional) Link CLI secara global:**
   ```sh
   npm link
   ```
   Setelah itu, gunakan:
   ```sh
   mockapi generate
   ```
   untuk membuat file endpoint mock baru.
4. **Cek server:**
   Endpoint `/test` tersedia untuk memastikan server berjalan.

## Contoh Endpoint
Misal ada file: `api/v1/marketing-program-retailer/backoffice/detail.js`
Maka endpoint: `GET /api/v1/marketing-program-retailer/backoffice/detail`

Contoh response (mock):
```json
{
  "header": {
    "name": "Program Penjualan Retailer Jan-April 2025",
    "status_enum": "SUBMITTED",
    ...
  },
  "information": { ... },
  "program_term": { ... },
  ...
}
```

## Pengembangan
- Tambahkan file JS di `/api` untuk endpoint baru.
- Gunakan CLI `mockapi generate` untuk scaffold otomatis.
- Semua route di-load otomatis oleh `server.js`.
- Untuk development, gunakan:
  ```sh
  npm run dev
  ```

## Deployment
- Sudah siap deploy ke Vercel (lihat `vercel.json`).
- Export default `app` dari `server.js` untuk kompatibilitas Vercel.

## Dependensi Utama
- express
- cors
- inquirer (CLI)
- nodemon, prettier (dev)

## Kontribusi
Feel free to submit issues or merge requests to improve Atom MockAPI.


